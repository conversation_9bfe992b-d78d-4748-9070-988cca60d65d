local ReplicatedStorage = game:GetService("ReplicatedStorage")

local PlantTraitsData = require(ReplicatedStorage.Modules.PlantTraitsData)
local GrowableData = require(ReplicatedStorage.Data.GrowableData)

local function getAllFruits()
    local fruitSet = {}
    local fruitList = {}

    -- Get fruits from PlantTraitsData "Fruit" category
    for fruitName, _ in pairs(PlantTraitsData.Fruit or {}) do
        if not fruitSet[fruitName] then
            fruitSet[fruitName] = true
            table.insert(fruitList, fruitName)
        end
    end

    -- Get additional fruits from GrowableData that might not be in PlantTraitsData
    -- Check all plants in GrowableData and add ones that have FruitData or are fruit-like
    for plantKey, plantData in pairs(GrowableData) do
        if type(plantData) == "table" and plantData.PlantName then
            local plantName = plantData.PlantName

            -- Add if it has FruitData (indicating it produces fruit) or if it's a known fruit
            if plantData.FruitData or
               plantName:find("Fruit") or
               plantName:find("Berry") or
               plantName:find("Melon") or
               plantName == "Tomato" or
               plantName == "Corn" then

                if not fruitSet[plantName] then
                    fruitSet[plantName] = true
                    table.insert(fruitList, plantName)
                end
            end
        end
    end

    -- Sort the list alphabetically
    table.sort(fruitList)

    return fruitList
end

-- Get and return the list of all fruits
local allFruits = getAllFruits()

print("All fruits in the game:")
for i, fruitName in ipairs(allFruits) do
    print(i .. ". " .. fruitName)
end

print("\nTotal fruits found: " .. #allFruits)

return allFruits